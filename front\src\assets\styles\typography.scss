// ===========================================
// 字体层级系统 - Typography System
// ===========================================

// 基础字体设置
html {
  font-size: 16px; // 基准字体大小
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  font-weight: var(--font-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

// 标题层级系统 - Heading System
.heading-1,
h1 {
  font-size: var(--text-4xl);
  line-height: var(--leading-tight);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  letter-spacing: -0.025em;
}

.heading-2,
h2 {
  font-size: var(--text-2xl);
  line-height: var(--leading-snug);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-5);
  letter-spacing: -0.015em;
}

.heading-3,
h3 {
  font-size: var(--text-xl);
  line-height: var(--leading-snug);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.heading-4,
h4 {
  font-size: var(--text-lg);
  line-height: var(--leading-normal);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.heading-5,
h5 {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.heading-6,
h6 {
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

// 正文层级系统 - Body Text System
.text-large {
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
  font-weight: var(--font-normal);
  color: var(--text-primary);
}

.text-base,
p {
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  font-weight: var(--font-normal);
  color: var(--text-primary);
  margin-bottom: var(--space-4);

  &:last-child {
    margin-bottom: 0;
  }
}

.text-small {
  font-size: var(--text-xs);
  line-height: var(--leading-normal);
  font-weight: var(--font-normal);
  color: var(--text-secondary);
}

.text-tiny {
  font-size: 0.6875rem; // 11px
  line-height: var(--leading-normal);
  font-weight: var(--font-normal);
  color: var(--text-tertiary);
}

// 功能性文字样式 - Functional Text Styles
.text-label {
  font-size: var(--text-xs);
  line-height: var(--leading-normal);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.text-caption {
  font-size: var(--text-xs);
  line-height: var(--leading-normal);
  font-weight: var(--font-normal);
  color: var(--text-tertiary);
  font-style: italic;
}

.text-code {
  font-family: var(--font-mono);
  font-size: var(--text-sm);
  background: var(--bg-tertiary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

// 链接样式 - Link Styles
.link {
  color: var(--brand-primary);
  text-decoration: none;
  font-weight: var(--font-medium);
  transition: color var(--transition-fast);

  &:hover {
    color: var(--brand-primary-dark);
    text-decoration: underline;
  }

  &:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
  }
}

.link-secondary {
  color: var(--brand-secondary);
  text-decoration: none;
  font-weight: var(--font-medium);
  transition: color var(--transition-fast);

  &:hover {
    color: var(--brand-secondary-light);
    text-decoration: underline;
  }
}

.link-subtle {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);

  &:hover {
    color: var(--text-primary);
  }
}

// 颜色修饰符 - Color Modifiers
.text-primary {
  color: var(--text-primary) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.text-tertiary {
  color: var(--text-tertiary) !important;
}

.text-disabled {
  color: var(--text-disabled) !important;
}

.text-inverse {
  color: var(--text-inverse) !important;
}

.text-brand {
  color: var(--brand-primary) !important;
}

.text-success {
  color: var(--color-success) !important;
}

.text-warning {
  color: var(--color-warning) !important;
}

.text-error {
  color: var(--color-error) !important;
}

.text-info {
  color: var(--color-info) !important;
}

// 字重修饰符 - Font Weight Modifiers
.font-thin {
  font-weight: var(--font-thin) !important;
}
.font-light {
  font-weight: var(--font-light) !important;
}
.font-normal {
  font-weight: var(--font-normal) !important;
}
.font-medium {
  font-weight: var(--font-medium) !important;
}
.font-semibold {
  font-weight: var(--font-semibold) !important;
}
.font-bold {
  font-weight: var(--font-bold) !important;
}
.font-extrabold {
  font-weight: var(--font-extrabold) !important;
}
.font-black {
  font-weight: var(--font-black) !important;
}

// 文本对齐修饰符 - Text Alignment Modifiers
.text-left {
  text-align: left !important;
}
.text-center {
  text-align: center !important;
}
.text-right {
  text-align: right !important;
}
.text-justify {
  text-align: justify !important;
}

// 文本转换修饰符 - Text Transform Modifiers
.text-uppercase {
  text-transform: uppercase !important;
}
.text-lowercase {
  text-transform: lowercase !important;
}
.text-capitalize {
  text-transform: capitalize !important;
}
.text-normal-case {
  text-transform: none !important;
}

// 文本装饰修饰符 - Text Decoration Modifiers
.text-underline {
  text-decoration: underline !important;
}
.text-line-through {
  text-decoration: line-through !important;
}
.text-no-underline {
  text-decoration: none !important;
}

// 文本截断 - Text Truncation
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 响应式字体大小 - Responsive Typography
@media (max-width: 768px) {
  .heading-1,
  h1 {
    font-size: var(--text-3xl);
  }

  .heading-2,
  h2 {
    font-size: var(--text-xl);
  }

  .text-large {
    font-size: var(--text-base);
  }
}

@media (max-width: 480px) {
  .heading-1,
  h1 {
    font-size: var(--text-2xl);
  }

  .heading-2,
  h2 {
    font-size: var(--text-lg);
  }
}
