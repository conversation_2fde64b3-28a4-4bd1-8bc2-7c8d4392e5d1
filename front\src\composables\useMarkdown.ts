import { ref, computed } from 'vue'
import MarkdownIt from 'markdown-it'
import Prism from 'prismjs'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-typescript'
import 'prismjs/components/prism-json'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-markup' // HTML
import 'prismjs/components/prism-jsx'
import 'prismjs/components/prism-python'
import 'prismjs/components/prism-java'
import 'prismjs/components/prism-sql'

// 标题接口定义
export interface MarkdownHeading {
  id: string
  level: number
  title: string
  anchor: string
}

// Markdown配置选项
export interface MarkdownOptions {
  html?: boolean
  breaks?: boolean
  linkify?: boolean
  typographer?: boolean
}

// 创建markdown-it实例
const createMarkdownInstance = (options: MarkdownOptions = {}) => {
  const md = new MarkdownIt({
    html: options.html ?? true,
    breaks: options.breaks ?? false,
    linkify: options.linkify ?? true,
    typographer: options.typographer ?? true,
    highlight: (str: string, lang: string) => {
      // 代码高亮处理
      if (lang && Prism.languages[lang]) {
        try {
          return Prism.highlight(str, Prism.languages[lang], lang)
        } catch {}
      }

      // 尝试自动检测语言
      try {
        return Prism.highlight(str, Prism.languages.javascript, 'javascript')
      } catch {}

      return ''
    },
  })

  // 添加锚点支持
  md.renderer.rules.heading_open = (tokens, idx, options, env, self) => {
    const token = tokens[idx]
    const title = tokens[idx + 1]?.content || ''
    const anchor = generateAnchor(title)

    // 添加id属性用于锚点跳转
    token.attrJoin('id', anchor)
    token.attrJoin('class', 'markdown-heading')

    return self.renderToken(tokens, idx, options)
  }

  // 代码块渲染优化，添加语言标识
  md.renderer.rules.fence = (tokens, idx, options) => {
    const token = tokens[idx]
    const info = token.info ? md.utils.unescapeAll(token.info).trim() : ''
    const langName = info ? info.split(/\s+/g)[0] : ''

    let highlighted = ''
    if (options.highlight) {
      highlighted =
        options.highlight(token.content, langName, '') || md.utils.escapeHtml(token.content)
    } else {
      highlighted = md.utils.escapeHtml(token.content)
    }

    const langAttr = langName ? ` data-lang="${langName}"` : ''

    return `<pre class="language-${langName || 'text'}"${langAttr}><code class="language-${langName || 'text'}">${highlighted}</code></pre>\n`
  }

  return md
}

// 生成锚点ID
const generateAnchor = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^\w\u4e00-\u9fff\s-]/g, '') // 保留中文、英文、数字、空格和连字符
    .replace(/\s+/g, '-') // 空格转连字符
    .replace(/-+/g, '-') // 多个连字符合并为一个
    .replace(/^-|-$/g, '') // 移除首尾连字符
}

// 提取标题列表
const extractHeadings = (html: string): MarkdownHeading[] => {
  const headings: MarkdownHeading[] = []
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')

  const headingElements = doc.querySelectorAll('h1, h2, h3, h4, h5, h6')

  headingElements.forEach((element, index) => {
    const level = parseInt(element.tagName.slice(1))
    const title = element.textContent || ''
    const anchor = element.getAttribute('id') || generateAnchor(title)

    headings.push({
      id: `heading-${index}`,
      level,
      title,
      anchor,
    })
  })

  return headings
}

// 主要的useMarkdown组合式函数
export const useMarkdown = (options: MarkdownOptions = {}) => {
  const markdownContent = ref('')
  const mdInstance = createMarkdownInstance(options)

  // 渲染后的HTML
  const renderedHtml = computed(() => {
    if (!markdownContent.value.trim()) return ''

    try {
      return mdInstance.render(markdownContent.value)
    } catch (error) {
      console.error('Markdown渲染错误:', error)
      return `<div class="markdown-error">渲染失败: ${error}</div>`
    }
  })

  // 提取的标题列表
  const headings = computed(() => {
    if (!renderedHtml.value) return []
    return extractHeadings(renderedHtml.value)
  })

  // 设置Markdown内容
  const setContent = (content: string) => {
    markdownContent.value = content
  }

  // 渲染Markdown内容（一次性）
  const renderMarkdown = (content: string): string => {
    try {
      return mdInstance.render(content)
    } catch (error) {
      console.error('Markdown渲染错误:', error)
      return `<div class="markdown-error">渲染失败: ${error}</div>`
    }
  }

  // 渲染内联Markdown（无段落包装）
  const renderInline = (content: string): string => {
    try {
      return mdInstance.renderInline(content)
    } catch (error) {
      console.error('内联Markdown渲染错误:', error)
      return content
    }
  }

  // 跳转到指定锚点
  const scrollToHeading = (anchor: string) => {
    const element = document.getElementById(anchor)
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      })

      // 添加高亮效果
      element.classList.add('highlight')
      setTimeout(() => {
        element.classList.remove('highlight')
      }, 2000)
    }
  }

  return {
    // 响应式数据
    markdownContent,
    renderedHtml,
    headings,

    // 方法
    setContent,
    renderMarkdown,
    renderInline,
    scrollToHeading,
  }
}

// 默认导出，用于简单场景
export default useMarkdown
