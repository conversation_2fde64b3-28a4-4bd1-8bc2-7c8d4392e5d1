/* 自定义Prism代码高亮主题 */

/* 基础样式重置 */
code[class*='language-'],
pre[class*='language-'] {
  color: #383a42;
  background: none;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

/* 代码块容器 */
pre[class*='language-'] {
  background: #fafafa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  margin: 20px 0;
  padding: 20px;
  overflow: auto;
  direction: ltr;
  text-align: left;
}

/* 行内代码 */
:not(pre) > code[class*='language-'] {
  background: #f6f8fa;
  padding: 0.1em 0.3em;
  border-radius: 3px;
  white-space: normal;
}

/* 语法高亮颜色 */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #a0a1a7;
  font-style: italic;
}

.token.punctuation {
  color: #383a42;
}

.token.property,
.token.tag,
.token.constant,
.token.symbol,
.token.deleted {
  color: #e45649;
}

.token.boolean,
.token.number {
  color: #986801;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #50a14f;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
  color: #383a42;
}

.token.atrule,
.token.attr-value,
.token.function,
.token.class-name {
  color: #4078f2;
}

.token.keyword {
  color: #a626a4;
}

.token.regex,
.token.important {
  color: #e45649;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

/* JSON 特殊处理 */
.language-json .token.property {
  color: #0451a5;
}

.language-json .token.string {
  color: #a31515;
}

.language-json .token.number,
.language-json .token.boolean {
  color: #09885a;
}

/* Bash/Shell 特殊处理 */
.language-bash .token.function,
.language-shell .token.function {
  color: #795da3;
}

/* HTML 特殊处理 */
.language-html .token.tag .token.punctuation {
  color: #383a42;
}

.language-html .token.tag .token.tag {
  color: #e45649;
}

.language-html .token.attr-name {
  color: #986801;
}

.language-html .token.attr-value {
  color: #50a14f;
}

/* CSS 特殊处理 */
.language-css .token.selector {
  color: #e45649;
}

.language-css .token.property {
  color: #0451a5;
}

/* JavaScript/TypeScript 特殊处理 */
.language-javascript .token.template-string .token.interpolation .token.interpolation-punctuation {
  color: #a626a4;
}

.language-typescript .token.builtin {
  color: #0451a5;
}

/* 选中状态 */
.token.selection {
  background: #b3d4fc;
}

/* 行号支持（如果需要） */
.line-numbers .line-numbers-rows {
  border-right: 1px solid #e1e4e8;
  background: #f6f8fa;
}

.line-numbers-rows > span:before {
  color: #a0a1a7;
}
