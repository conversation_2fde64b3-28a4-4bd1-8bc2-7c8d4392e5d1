import type { Instructor, Course, LearningPath, CourseCategory } from '@/types/course'

// Mock 讲师数据
export const mockInstructors: Instructor[] = [
  {
    id: '1',
    name: '张三',
    title: '高级前端架构师',
    description:
      '10年前端开发经验，精通Vue.js生态系统，曾在腾讯、阿里巴巴等知名互联网公司担任技术专家。',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    coursesCount: 15,
    studentsCount: 25000,
    specialties: ['Vue.js', 'React', 'TypeScript', '前端架构'],
    experience: 10,
  },
  {
    id: '2',
    name: '李四',
    title: '全栈工程师',
    description: '专注于Node.js和微服务架构设计，拥有丰富的大型项目开发经验。',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    coursesCount: 12,
    studentsCount: 18000,
    specialties: ['Node.js', '微服务', 'Docker', 'Kubernetes'],
    experience: 8,
  },
  {
    id: '3',
    name: '王五',
    title: 'AI算法专家',
    description: '深度学习和自然语言处理领域专家，发表多篇顶级会议论文。',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    coursesCount: 8,
    studentsCount: 12000,
    specialties: ['机器学习', '深度学习', 'Python', 'TensorFlow'],
    experience: 12,
  },
  {
    id: '4',
    name: '赵六',
    title: '移动端架构师',
    description: 'React Native和Flutter跨平台开发专家，曾主导多个千万级用户的移动应用开发。',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    coursesCount: 10,
    studentsCount: 15000,
    specialties: ['React Native', 'Flutter', 'iOS', 'Android'],
    experience: 9,
  },
]

// Mock 课程数据
export const courses: Course[] = [
  {
    id: '1',
    title: 'Vue3 从零到一开发整站',
    description:
      '基于Vue3和组合式API的现代前端开发实战，带你从零开始构建完整的Web应用。课程涵盖Vue3核心特性、路由管理、状态管理、UI组件库等内容。',
    image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    price: 199,
    originalPrice: 399,
    category: '前端开发',
    level: 'intermediate',
    difficulty: '中级',
    badge: '热门',
    instructor: mockInstructors[0],
    studentsCount: 2845,
    rating: 4.8,
    reviewCount: 324,
    duration: '25课时',
    totalLessons: 25,
    chaptersCount: 5,
    skills: ['Vue3', 'TypeScript', 'Vite', 'Element Plus', 'Pinia'],
    requirements: ['HTML/CSS基础', 'JavaScript ES6+', '有一定编程经验'],
    features: ['25+个实战案例', '源码+笔记下载', '微信群答疑', '就业指导'],
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20',
    isHot: true,
    chapters: [
      {
        id: '1-1',
        title: '开篇',
        duration: '7分26秒',
        lessons: [
          {
            id: '1-1-1',
            title: '大家好！我是智贝豆',
            duration: '7分26秒',
            type: 'document',
            completed: false,
            locked: false,
            content: `# 大家好！我是智贝豆

在软件开发被 AI 深度重塑的今天，一场特时的革命正在改变我们编程的方式。当我们还在讨论 AI 是否会取代程序员时，聪明的开发者们已经满怀地，未来的核心竞争力，不再是百以缓夜地"撸码"，而是学会如何与 AI 高效协作，将 AI 变为自己能力的延伸。

## 为什么是 Trae?

我们正处在软件开发范式被彻底重塑的时代，AI 不再是简单的"代码补全工具"，而是能够自主思考和执行任务的"开发伙伴"。

### 你是否曾经历过这样的场景？

- 😰 面对一个新项目，需要花费数小时搭建基础架构
- 🤔 为了实现一个看似简单的功能，却要查阅大量文档和示例
- 😅 面对新技术或新语言，感觉无从下手，望而却步
- 📋 在重复性的 CRUD 代码中消耗创造力
- 🎯 想要快速验证一个想法，却被技术实现的复杂度阻碍步

如果你的答案是"是"，那么这本小册将彻底改变你的开发体验。

### 你能学到什么?

通过这门课程，你将掌握：

1. **AI 协作编程的核心理念**
2. **Trae 工具的完整使用方法**
3. **高效的代码生成技巧**
4. **实际项目开发经验**

### 适合什么样的你?

- 💻 有一定编程基础的开发者
- 🚀 希望提升开发效率的程序员
- 🎯 想要学习AI辅助编程的技术人员
- 📈 追求技术前沿的学习者

### 你将获得什么?

完成课程后，你将能够：

- ✅ 熟练使用 Trae 进行 AI 辅助开发
- ✅ 大幅提升编程效率和代码质量
- ✅ 掌握前沿的 AI 协作编程技能
- ✅ 获得完整的项目实战经验

### 写在最后

这不仅仅是一门技术课程，更是一次思维方式的转变。让我们一起拥抱 AI 时代的编程新范式！

![课程特色图](https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg)

> 📚 **学习建议**: 边学边练，理论结合实践，才能真正掌握 AI 协作编程的精髓。`,
          },
        ],
      },
      {
        id: '1-2',
        title: '基础篇 | Trae 双版本安装配置指南',
        duration: '13分18秒',
        lessons: [
          {
            id: '1-2-1',
            title: 'Trae 安装与环境配置',
            duration: '8分45秒',
            type: 'video',
            completed: false,
            locked: false,
            videoUrl: 'https://media.w3.org/2010/05/sintel/trailer.mp4',
            content: `## Trae 安装与环境配置

本节课程将详细介绍如何安装和配置 Trae 开发环境。

### 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Linux
- **Node.js**: 版本 16.0 或更高
- **内存**: 至少 4GB RAM
- **磁盘空间**: 至少 1GB 可用空间

### 安装步骤

#### 1. 下载 Trae

访问 [Trae 官网](https://trae.ai) 下载对应系统的安装包。

#### 2. 安装过程

\`\`\`bash
# Windows
trae-installer.exe

# macOS
sudo installer -pkg trae-installer.pkg -target /

# Linux
sudo dpkg -i trae-installer.deb
\`\`\`

#### 3. 验证安装

\`\`\`bash
trae --version
\`\`\`

### 配置开发环境

1. **API Key 配置**: 在用户设置中添加你的 API 密钥
2. **编辑器插件**: 安装对应编辑器的 Trae 插件
3. **项目初始化**: 在项目中运行 \`trae init\`

![Trae 配置界面](https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg)

#### 高级配置示例

\`\`\`javascript
// trae.config.js - 高级配置示例
module.exports = {
  // 模型配置
  model: {
    provider: 'openai',
    name: 'gpt-4',
    temperature: 0.7,
    maxTokens: 4096
  },

  // 项目设置
  project: {
    language: 'typescript',
    framework: 'vue',
    patterns: [
      'src/**/*.{ts,vue,js}',
      '!node_modules/**',
      '!dist/**'
    ]
  },

  // 自定义提示词
  prompts: {
    generate: 'Generate high-quality, production-ready code',
    refactor: 'Refactor this code following best practices',
    explain: 'Explain this code in simple terms'
  }
}
\`\`\`

\`\`\`bash
# 环境变量配置
export TRAE_API_KEY="your-api-key-here"
export TRAE_MODEL="gpt-4"
export TRAE_TEMPERATURE="0.7"
\`\`\`

> 💡 **提示**: 首次使用建议先完成官方教程，熟悉基本操作流程。

### 常见问题

| 问题 | 解决方案 |
|------|----------|
| 安装失败 | 检查网络连接和权限设置 |
| API 错误 | 验证 API Key 是否正确 |
| 插件无响应 | 重启编辑器并重新加载插件 |`,
          },
          {
            id: '1-2-2',
            title: '项目初始化与基本配置',
            duration: '4分33秒',
            type: 'video',
            completed: false,
            locked: false,
            videoUrl: 'https://media.w3.org/2010/05/sintel/trailer.mp4',
            content: `## 项目初始化与基本配置

学会如何在现有项目中集成 Trae，以及基本的配置选项。

### 快速开始

\`\`\`bash
# 创建新项目
mkdir my-trae-project
cd my-trae-project

# 初始化 Trae
trae init
\`\`\`

### 配置文件解析

Trae 会在项目根目录生成 \`.trae\` 配置文件：

\`\`\`json
{
  "model": "gpt-4",
  "temperature": 0.7,
  "maxTokens": 2048,
  "includes": ["src/**/*.js", "src/**/*.ts"],
  "excludes": ["node_modules", "dist"]
}
\`\`\`

### 基本命令

- \`trae generate\`: 生成代码
- \`trae refactor\`: 重构代码
- \`trae explain\`: 解释代码
- \`trae test\`: 生成测试

> ⚠️ **注意**: 确保项目目录有足够的权限进行文件操作。`,
          },
        ],
      },
      {
        id: '1-3',
        title: '基础篇 | Node.js 开发环境安装与配置',
        duration: '9分29秒',
        lessons: [
          {
            id: '1-3-1',
            title: 'Node.js 环境搭建',
            duration: '6分15秒',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '1-3-2',
            title: 'npm 包管理器使用',
            duration: '3分14秒',
            type: 'video',
            completed: false,
            locked: false,
          },
        ],
      },
      {
        id: '1-4',
        title: '基础篇 | Agent: Trae 的核心能力',
        duration: '20分38秒',
        lessons: [
          {
            id: '1-4-1',
            title: 'Agent 概念详解',
            duration: '8分22秒',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '1-4-2',
            title: 'Trae Agent 实战示例',
            duration: '12分16秒',
            type: 'video',
            completed: false,
            locked: false,
          },
        ],
      },
      {
        id: '1-5',
        title: '基础篇 | Cue: 不止于 Tab 补全',
        duration: '13分46秒',
        lessons: [
          {
            id: '1-5-1',
            title: 'Cue 功能介绍',
            duration: '7分28秒',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '1-5-2',
            title: '高级补全技巧',
            duration: '6分18秒',
            type: 'video',
            completed: false,
            locked: false,
          },
        ],
      },
      {
        id: '1-6',
        title: '基础篇 | 内联聊天: 你的贴身 AI 编码助手',
        duration: '7分36秒',
        lessons: [
          {
            id: '1-6-1',
            title: '内联聊天功能详解',
            duration: '7分36秒',
            type: 'video',
            completed: false,
            locked: false,
          },
        ],
      },
    ],
  },
  {
    id: '2',
    title: 'Node.js 全栈开发',
    description:
      '掌握服务端JavaScript开发，学习Express框架、数据库操作、API设计等后端开发核心技能。',
    image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    price: 299,
    category: '后端开发',
    level: 'intermediate',
    difficulty: '中级',
    instructor: mockInstructors[1],
    studentsCount: 1923,
    rating: 4.7,
    reviewCount: 245,
    duration: '32课时',
    totalLessons: 32,
    chaptersCount: 8,
    skills: ['Node.js', 'Express', 'MongoDB', 'RESTful API'],
    requirements: ['JavaScript基础', '了解HTTP协议'],
    features: ['实战项目', '代码示例', '在线答疑'],
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18',
    chapters: [
      {
        id: '2-1',
        title: 'Node.js 基础',
        duration: '45分钟',
        lessons: [
          {
            id: '2-1-1',
            title: 'Node.js 简介与安装',
            duration: '15分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '2-1-2',
            title: '模块系统与 NPM',
            duration: '20分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '2-1-3',
            title: '异步编程基础',
            duration: '10分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
        ],
      },
      {
        id: '2-2',
        title: 'Express 框架',
        duration: '60分钟',
        lessons: [
          {
            id: '2-2-1',
            title: 'Express 快速入门',
            duration: '25分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '2-2-2',
            title: '路由与中间件',
            duration: '35分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
        ],
      },
    ],
  },
  {
    id: '3',
    title: 'AI 大模型应用开发',
    description: '从零开始学习大语言模型应用开发，掌握ChatGPT、Claude等AI工具的使用和集成开发。',
    image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    price: 399,
    originalPrice: 699,
    category: 'AI人工智能',
    level: 'advanced',
    difficulty: '高级',
    badge: '新课',
    instructor: mockInstructors[2],
    studentsCount: 856,
    rating: 4.9,
    reviewCount: 128,
    duration: '45课时',
    totalLessons: 45,
    chaptersCount: 10,
    skills: ['Python', 'OpenAI API', '机器学习', '自然语言处理'],
    requirements: ['Python基础', '机器学习概念'],
    features: ['最新技术', 'API实战', '项目作品'],
    createdAt: '2024-02-01',
    updatedAt: '2024-02-05',
    isNew: true,
    chapters: [
      {
        id: '3-1',
        title: 'AI 基础概念',
        duration: '90分钟',
        lessons: [
          {
            id: '3-1-1',
            title: '人工智能发展史',
            duration: '30分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '3-1-2',
            title: '大语言模型原理',
            duration: '45分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '3-1-3',
            title: 'Transformer 架构解析',
            duration: '15分钟',
            type: 'document',
            completed: false,
            locked: false,
          },
        ],
      },
      {
        id: '3-2',
        title: 'OpenAI API 实战',
        duration: '120分钟',
        lessons: [
          {
            id: '3-2-1',
            title: 'API 接口介绍',
            duration: '30分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '3-2-2',
            title: '文本生成实战',
            duration: '45分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '3-2-3',
            title: '智能对话系统开发',
            duration: '45分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
        ],
      },
    ],
  },
  {
    id: '4',
    title: 'React Native 移动开发',
    description: '使用React Native开发跨平台移动应用，一套代码同时支持iOS和Android平台。',
    image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    price: 259,
    category: '移动开发',
    level: 'intermediate',
    difficulty: '中级',
    instructor: mockInstructors[3],
    studentsCount: 1456,
    rating: 4.6,
    reviewCount: 189,
    duration: '28课时',
    totalLessons: 28,
    chaptersCount: 7,
    skills: ['React Native', 'Redux', '原生模块', '性能优化'],
    requirements: ['React基础', 'JavaScript ES6+'],
    features: ['真机调试', '上架指导', '性能优化'],
    createdAt: '2024-01-25',
    updatedAt: '2024-01-30',
    chapters: [
      {
        id: '4-1',
        title: 'React Native 基础',
        duration: '75分钟',
        lessons: [
          {
            id: '4-1-1',
            title: '环境搭建与项目创建',
            duration: '25分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '4-1-2',
            title: '组件与样式',
            duration: '30分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '4-1-3',
            title: '导航与路由',
            duration: '20分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
        ],
      },
      {
        id: '4-2',
        title: '状态管理与数据',
        duration: '90分钟',
        lessons: [
          {
            id: '4-2-1',
            title: 'Redux 状态管理',
            duration: '45分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
          {
            id: '4-2-2',
            title: '网络请求与API',
            duration: '45分钟',
            type: 'video',
            completed: false,
            locked: false,
          },
        ],
      },
    ],
  },
]

// Mock 课程分类数据
export const mockCategories: CourseCategory[] = [
  {
    id: '1',
    title: 'PAG 低手升级',
    description: '零基础小白的PAG小技巧，快速入门图形编程',
    icon: 'Monitor',
    path: '/category/pag',
    coursesCount: 12,
    studentsCount: 5600,
  },
  {
    id: '2',
    title: 'AI Agent 开发入门',
    description: '大模型 AI Agent 开发实践，掌握智能助手开发',
    icon: 'Cpu',
    path: '/category/ai',
    coursesCount: 8,
    studentsCount: 3200,
  },
  {
    id: '3',
    title: '软件架构师',
    description: '软件架构师精修课程，提升系统设计能力',
    icon: 'Setting',
    path: '/category/architecture',
    coursesCount: 15,
    studentsCount: 8900,
  },
  {
    id: '4',
    title: 'MCP 实战',
    description: 'MCP 协议深度解析，掌握模型通信协议',
    icon: 'Connection',
    path: '/category/mcp',
    coursesCount: 6,
    studentsCount: 2100,
  },
]

// Mock 学习路径数据
export const mockLearningPaths: LearningPath[] = [
  {
    id: '1',
    title: 'AI大模型前沿知识',
    description: 'ChatGPT、AI Agent及深度学习的完整学习路径，从基础概念到实战应用。',
    image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    category: 'ai',
    level: 'intermediate',
    courses: [courses[2], courses[0]], // AI课程 + Vue3课程
    totalDuration: 819, // 所有课程总时长
    studentsCount: 1560,
    rating: 4.8,
    price: 599,
    originalPrice: 998,
    instructor: mockInstructors[2],
    skills: ['AI开发', 'Python', '机器学习', 'Web开发'],
    createdAt: '2024-02-01',
  },
  {
    id: '2',
    title: '全栈工程师养成',
    description: '从前端到后端的完整技术栈学习，成为独当一面的全栈开发者。',
    image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    category: 'fullstack',
    level: 'advanced',
    courses: [courses[0], courses[1]], // Vue3 + Node.js
    totalDuration: 655,
    studentsCount: 2340,
    rating: 4.7,
    price: 459,
    originalPrice: 698,
    instructor: mockInstructors[1],
    skills: ['前端开发', '后端开发', '数据库', '部署运维'],
    createdAt: '2024-01-20',
  },
  {
    id: '3',
    title: '移动开发专家',
    description: '掌握移动端开发技术，包括原生开发和跨平台开发解决方案。',
    image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    category: 'mobile',
    level: 'intermediate',
    courses: [courses[3]], // React Native
    totalDuration: 320,
    studentsCount: 890,
    rating: 4.6,
    price: 299,
    originalPrice: 459,
    instructor: mockInstructors[3],
    skills: ['移动开发', '跨平台', 'React Native', 'Flutter'],
    createdAt: '2024-01-25',
  },
]

// Mock 轮播图数据 - 基于用户提供的宽屏图片优化
export const mockBanners = [
  {
    id: 1,
    title: '100+ 小时热门好课免费学',
    image: '/banners/ai-course-banner.png', // AI大模型|软考|Java|Go|架构|云原生
    link: '/courses?category=ai',
  },
  {
    id: 2,
    title: 'AI时代企业产品能力建设培养项目',
    image: '/banners/ai-enterprise-banner.png', // 用AI重构产品增长新曲线
    link: '/courses?category=ai-enterprise',
  },
  {
    id: 3,
    title: 'MCP实战课程',
    image: '/banners/mcp-course-banner.png', // 4个实战项目实现MCP从入门到进阶
    link: '/courses?category=mcp',
  },
  {
    id: 4,
    title: '大模型安全实战课',
    image: '/banners/security-course-banner.png', // 从认知到防御，全方位建设大模型安全防线
    link: '/courses?category=security',
  },
]
