<template>
  <div class="learning-paths-overview">
    <!-- 简洁实用的页面头部 -->
    <section class="header-section">
      <div class="container">
        <div class="header-content">
          <div class="title-area">
            <h1 class="page-title">学习路径</h1>
            <p class="page-subtitle">选择适合你的技能成长路线，系统化提升专业能力</p>
          </div>

          <!-- 快速筛选栏 -->
          <div class="quick-filters">
            <div class="filter-group">
              <span class="filter-label">分类</span>
              <div class="filter-chips">
                <span
                  v-for="category in categories"
                  :key="category.id"
                  class="filter-chip"
                  :class="{ active: selectedCategory === category.id }"
                  @click="setCategory(category.id)"
                >
                  <i :class="category.icon"></i>
                  {{ category.name }}
                </span>
              </div>
            </div>

            <div class="filter-group">
              <span class="filter-label">难度</span>
              <div class="filter-chips">
                <span
                  v-for="level in levels"
                  :key="level.id"
                  class="filter-chip level-chip"
                  :class="{ active: selectedLevel === level.id, [level.id]: true }"
                  @click="setLevel(level.id)"
                >
                  {{ level.name }}
                </span>
              </div>
            </div>

            <div class="search-group">
              <el-input
                v-model="searchQuery"
                placeholder="搜索学习路径..."
                class="search-input"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 学习路径网格区域 -->
    <section class="paths-section">
      <div class="container">
        <!-- 结果统计 -->
        <div class="results-header">
          <div class="results-info">
            <span class="results-count"
              >找到 <strong>{{ filteredPaths.length }}</strong> 个学习路径</span
            >
            <span class="total-info"
              >共 {{ totalCourses }} 门课程 · {{ totalStudents }} 位学员</span
            >
          </div>
          <div class="view-controls">
            <el-button v-if="hasActiveFilters" @click="resetFilters" type="text" class="reset-btn">
              清除筛选
            </el-button>
          </div>
        </div>

        <!-- 创新的学习路径网格布局 -->
        <div class="paths-grid">
          <div
            v-for="path in filteredPaths"
            :key="path.id"
            class="path-card"
            :class="{
              featured: path.isHot,
              'in-progress': path.learningStatus === 'in_progress',
              completed: path.learningStatus === 'completed',
              [path.level]: true,
            }"
            @click="goToPathDetail(path.id)"
          >
            <!-- 路径标识区域 -->
            <div class="path-header">
              <div class="path-icon">
                <i :class="getCategoryIcon(path.category)"></i>
              </div>
              <div class="path-badges">
                <span class="level-badge" :class="path.level">
                  {{ getLevelText(path.level) }}
                </span>
                <span v-if="path.isHot" class="hot-badge">🔥 热门</span>
                <span v-if="path.learningStatus === 'in_progress'" class="status-badge in-progress"
                  >📚 学习中</span
                >
                <span v-if="path.learningStatus === 'completed'" class="status-badge completed"
                  >✅ 已完成</span
                >
              </div>
            </div>

            <!-- 路径主要信息 -->
            <div class="path-main">
              <h3 class="path-title">{{ path.title }}</h3>
              <p class="path-description">{{ path.description }}</p>

              <!-- 学习路径预览 -->
              <div class="path-preview">
                <div class="skill-roadmap">
                  <div class="roadmap-title">技能路线</div>
                  <div class="skills-flow">
                    <span
                      v-for="(skill, idx) in path.skills.slice(0, 4)"
                      :key="skill"
                      class="skill-node"
                      :style="{ animationDelay: idx * 0.1 + 's' }"
                    >
                      {{ skill }}
                    </span>
                    <span v-if="path.skills.length > 4" class="more-skills">
                      +{{ path.skills.length - 4 }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 路径统计信息 -->
            <div class="path-stats">
              <div class="stat-row">
                <div class="stat-item">
                  <el-icon class="stat-icon"><Reading /></el-icon>
                  <span class="stat-value">{{ path.courses.length }}</span>
                  <span class="stat-label">门课程</span>
                </div>
                <div class="stat-item">
                  <el-icon class="stat-icon"><Clock /></el-icon>
                  <span class="stat-value">{{ formatDuration(path.totalDuration) }}</span>
                  <span class="stat-label">学时</span>
                </div>
                <div class="stat-item">
                  <el-icon class="stat-icon"><User /></el-icon>
                  <span class="stat-value">{{ formatStudentsCount(path.studentsCount) }}</span>
                  <span class="stat-label">学员</span>
                </div>
              </div>

              <div class="rating-row">
                <el-rate
                  v-model="path.rating"
                  disabled
                  size="small"
                  show-score
                  score-template="{value}"
                />
              </div>
            </div>

            <!-- 价格和操作区域 -->
            <div class="path-footer">
              <div class="price-section">
                <span v-if="path.originalPrice" class="original-price"
                  >¥{{ path.originalPrice }}</span
                >
                <span class="current-price">¥{{ path.price }}</span>
              </div>
              <div class="action-section">
                <el-button type="primary" class="start-btn" @click.stop="startLearning(path)">
                  开始学习
                </el-button>
              </div>
            </div>

            <!-- 悬停时的详细信息层 -->
            <div class="hover-overlay">
              <div class="overlay-content">
                <h4>课程大纲预览</h4>
                <div class="course-preview">
                  <div
                    v-for="course in path.courses.slice(0, 3)"
                    :key="course.id"
                    class="course-item"
                  >
                    <span class="course-title">{{ course.title }}</span>
                    <span class="course-duration">{{ course.duration }}</span>
                  </div>
                  <div v-if="path.courses.length > 3" class="more-courses">
                    还有 {{ path.courses.length - 3 }} 门课程...
                  </div>
                </div>
                <el-button type="primary" size="small" class="preview-btn">
                  查看完整路径
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredPaths.length === 0" class="empty-state">
          <div class="empty-illustration">
            <i class="empty-icon">🔍</i>
          </div>
          <h3 class="empty-title">没有找到匹配的学习路径</h3>
          <p class="empty-description">试试调整筛选条件或搜索其他关键词</p>
          <el-button @click="resetFilters" type="primary">重置筛选条件</el-button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search, Reading, Clock, User } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { LearningPath } from '@/types/course'
import { mockLearningPaths } from '@/data/mockData'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('all')
const selectedLevel = ref('all')
const learningPaths = ref<LearningPath[]>([])

// 筛选选项 - 添加图标
const categories = ref([
  { id: 'all', name: '全部', icon: 'el-icon-menu' },
  { id: 'ai', name: 'AI/大数据', icon: 'el-icon-cpu' },
  { id: 'frontend', name: '前端', icon: 'el-icon-monitor' },
  { id: 'backend', name: '后端', icon: 'el-icon-server' },
  { id: 'fullstack', name: '全栈', icon: 'el-icon-connection' },
  { id: 'mobile', name: '移动端', icon: 'el-icon-mobile-phone' },
  { id: 'devops', name: '运维', icon: 'el-icon-setting' },
])

const levels = ref([
  { id: 'all', name: '全部难度' },
  { id: 'beginner', name: '入门' },
  { id: 'intermediate', name: '进阶' },
  { id: 'advanced', name: '高级' },
])

// 计算属性
const filteredPaths = computed(() => {
  let result = learningPaths.value

  // 搜索过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(
      (path) =>
        path.title.toLowerCase().includes(query) ||
        path.description.toLowerCase().includes(query) ||
        path.skills.some((skill) => skill.toLowerCase().includes(query)),
    )
  }

  // 分类过滤
  if (selectedCategory.value !== 'all') {
    result = result.filter((path) => path.category === selectedCategory.value)
  }

  // 难度过滤
  if (selectedLevel.value !== 'all') {
    result = result.filter((path) => path.level === selectedLevel.value)
  }

  return result
})

const totalCourses = computed(() =>
  learningPaths.value.reduce((total, path) => total + path.courses.length, 0),
)

const totalStudents = computed(() => {
  const total = learningPaths.value.reduce((sum, path) => sum + path.studentsCount, 0)
  return Math.round(total / 1000) + 'k+'
})

const hasActiveFilters = computed(
  () =>
    selectedCategory.value !== 'all' ||
    selectedLevel.value !== 'all' ||
    searchQuery.value.trim() !== '',
)

// 方法
const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
}

const setCategory = (categoryId: string) => {
  selectedCategory.value = categoryId
}

const setLevel = (levelId: string) => {
  selectedLevel.value = levelId
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = 'all'
  selectedLevel.value = 'all'
}

const getLevelText = (level: string) => {
  const levelMap: Record<string, string> = {
    beginner: '入门',
    intermediate: '进阶',
    advanced: '高级',
  }
  return levelMap[level] || level
}

const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, string> = {
    ai: 'el-icon-cpu',
    frontend: 'el-icon-monitor',
    backend: 'el-icon-server',
    fullstack: 'el-icon-connection',
    mobile: 'el-icon-mobile-phone',
    devops: 'el-icon-setting',
  }
  return iconMap[category] || 'el-icon-document'
}

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60)
  return `${hours}h`
}

const formatStudentsCount = (count: number) => {
  if (count >= 10000) {
    return `${Math.floor(count / 10000)}w`
  } else if (count >= 1000) {
    return `${Math.floor(count / 1000)}k`
  }
  return count.toString()
}

const goToPathDetail = (pathId: string) => {
  router.push(`/path/${pathId}`)
}

const startLearning = (path: LearningPath) => {
  ElMessage.success(`开始学习《${path.title}》`)
  router.push(`/path/${path.id}`)
}

// 生命周期
onMounted(() => {
  // 扩展学习路径数据
  learningPaths.value = [
    ...mockLearningPaths,
    {
      id: '4',
      title: '前端工程师成长路线',
      description: '从HTML/CSS基础到现代前端框架，系统掌握前端开发的核心技能和最佳实践。',
      image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
      category: 'frontend',
      level: 'beginner',
      courses: mockLearningPaths[0].courses.slice(0, 1),
      totalDuration: 480,
      studentsCount: 5680,
      rating: 4.6,
      price: 299,
      originalPrice: 499,
      instructor: mockLearningPaths[0].instructor,
      skills: ['HTML5', 'CSS3', 'JavaScript', 'Vue.js', '响应式设计'],
      createdAt: '2024-01-15',
      isHot: true,
      learningStatus: 'in_progress',
    },
    {
      id: '5',
      title: '后端架构师养成计划',
      description: '从后端开发基础到分布式架构设计，成为技术领域专家的完整成长路径。',
      image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
      category: 'backend',
      level: 'advanced',
      courses: mockLearningPaths[1].courses,
      totalDuration: 720,
      studentsCount: 3240,
      rating: 4.8,
      price: 699,
      originalPrice: 999,
      instructor: mockLearningPaths[1].instructor,
      skills: ['Node.js', '微服务架构', '数据库设计', '系统设计', '性能优化'],
      createdAt: '2024-01-10',
    },
    {
      id: '6',
      title: 'DevOps工程师实战训练',
      description: '掌握现代化运维技术栈，从容器化部署到CI/CD自动化的完整实践。',
      image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
      category: 'devops',
      level: 'intermediate',
      courses: mockLearningPaths[0].courses.slice(0, 2),
      totalDuration: 560,
      studentsCount: 2100,
      rating: 4.7,
      price: 399,
      originalPrice: 599,
      instructor: mockLearningPaths[2].instructor,
      skills: ['Docker', 'Kubernetes', 'Jenkins', 'AWS', '监控运维'],
      createdAt: '2024-01-05',
    },
    {
      id: '7',
      title: '数据科学与机器学习',
      description: '从数据分析基础到机器学习算法应用，培养数据科学思维和实践能力。',
      image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
      category: 'ai',
      level: 'intermediate',
      courses: mockLearningPaths[0].courses,
      totalDuration: 650,
      studentsCount: 4200,
      rating: 4.9,
      price: 599,
      originalPrice: 899,
      instructor: mockLearningPaths[0].instructor,
      skills: ['Python', '数据分析', '机器学习', 'TensorFlow', '深度学习'],
      createdAt: '2024-01-08',
      isHot: true,
      learningStatus: 'completed',
    },
  ]
})
</script>

<style scoped lang="scss">
.learning-paths-overview {
  min-height: 100vh;
  background: #f8fafc;
}

// 简洁的页面头部
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 0;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 40px;

    .title-area {
      flex: 1;

      .page-title {
        font-size: 32px;
        font-weight: 700;
        margin: 0 0 8px 0;
        line-height: 1.2;
      }

      .page-subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
        line-height: 1.4;
      }
    }

    .quick-filters {
      display: flex;
      gap: 32px;
      align-items: flex-start;
      flex: 2;

      .filter-group {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .filter-label {
          font-size: 12px;
          opacity: 0.8;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .filter-chips {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          .filter-chip {
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 4px;

            i {
              font-size: 12px;
            }

            &:hover {
              background: rgba(255, 255, 255, 0.25);
              transform: translateY(-1px);
            }

            &.active {
              background: rgba(255, 255, 255, 0.9);
              color: #667eea;
              border-color: rgba(255, 255, 255, 0.9);
            }

            &.level-chip {
              &.beginner.active {
                background: #52c41a;
                color: white;
                border-color: #52c41a;
              }

              &.intermediate.active {
                background: #fa8c16;
                color: white;
                border-color: #fa8c16;
              }

              &.advanced.active {
                background: #f5222d;
                color: white;
                border-color: #f5222d;
              }
            }
          }
        }
      }

      .search-group {
        min-width: 240px;

        .search-input {
          :deep(.el-input__wrapper) {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 20px;

            .el-input__inner {
              color: white;

              &::placeholder {
                color: rgba(255, 255, 255, 0.7);
              }
            }

            .el-input__prefix {
              .el-icon {
                color: rgba(255, 255, 255, 0.8);
              }
            }
          }
        }
      }
    }
  }
}

// 内容区域
.paths-section {
  padding: 32px 0 60px;

  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;

    .results-info {
      display: flex;
      align-items: center;
      gap: 16px;

      .results-count {
        font-size: 16px;
        color: #1a202c;

        strong {
          color: #667eea;
          font-weight: 600;
        }
      }

      .total-info {
        font-size: 14px;
        color: #718096;
      }
    }

    .view-controls {
      .reset-btn {
        color: #667eea;
      }
    }
  }
}

// 创新的网格布局
.paths-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 24px;

  .path-card {
    position: relative;
    background: white;
    border-radius: 16px;
    border: 2px solid transparent;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 320px;
    display: flex;
    flex-direction: column;

    // 不同难度的边框颜色
    &.beginner {
      border-left: 4px solid #52c41a;
    }

    &.intermediate {
      border-left: 4px solid #fa8c16;
    }

    &.advanced {
      border-left: 4px solid #f5222d;
    }

    // 热门路径的特殊样式
    &.featured {
      border: 2px solid #ffd700;
      box-shadow: 0 8px 32px rgba(255, 215, 0, 0.2);
    }

    // 学习中状态
    &.in-progress {
      border: 2px solid #1890ff;
      box-shadow: 0 4px 20px rgba(24, 144, 255, 0.15);
    }

    // 已完成状态
    &.completed {
      border: 2px solid #52c41a;
      box-shadow: 0 4px 20px rgba(82, 196, 26, 0.15);
    }

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border-color: #667eea;

      .hover-overlay {
        opacity: 1;
        visibility: visible;
      }

      .skill-node {
        animation: pulse 1.5s ease-in-out infinite;
      }
    }

    .path-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 20px 20px 0;

      .path-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
      }

      .path-badges {
        display: flex;
        gap: 8px;

        .level-badge {
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 600;
          color: white;

          &.beginner {
            background: #52c41a;
          }

          &.intermediate {
            background: #fa8c16;
          }

          &.advanced {
            background: #f5222d;
          }
        }

        .hot-badge {
          padding: 4px 8px;
          background: linear-gradient(45deg, #ff6b6b, #ffd93d);
          color: white;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 600;
        }

        .status-badge {
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 600;

          &.in-progress {
            background: #1890ff;
            color: white;
          }

          &.completed {
            background: #52c41a;
            color: white;
          }
        }
      }
    }

    .path-main {
      padding: 16px 20px;
      flex: 1;

      .path-title {
        font-size: 18px;
        font-weight: 600;
        color: #1a202c;
        margin: 0 0 8px 0;
        line-height: 1.4;
      }

      .path-description {
        font-size: 14px;
        color: #4a5568;
        line-height: 1.5;
        margin: 0 0 16px 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .path-preview {
        .skill-roadmap {
          .roadmap-title {
            font-size: 12px;
            color: #718096;
            margin-bottom: 8px;
            font-weight: 500;
          }

          .skills-flow {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;

            .skill-node {
              padding: 4px 10px;
              background: linear-gradient(135deg, #667eea, #764ba2);
              color: white;
              border-radius: 14px;
              font-size: 11px;
              font-weight: 500;
              position: relative;

              &:not(:last-child)::after {
                content: '→';
                position: absolute;
                right: -12px;
                top: 50%;
                transform: translateY(-50%);
                color: #cbd5e0;
                font-size: 10px;
              }
            }

            .more-skills {
              padding: 4px 10px;
              background: #e2e8f0;
              color: #4a5568;
              border-radius: 14px;
              font-size: 11px;
              font-weight: 500;
            }
          }
        }
      }
    }

    .path-stats {
      padding: 0 20px 16px;

      .stat-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 2px;

          .stat-icon {
            color: #667eea;
            font-size: 16px;
          }

          .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #1a202c;
          }

          .stat-label {
            font-size: 11px;
            color: #718096;
          }
        }
      }

      .rating-row {
        display: flex;
        justify-content: center;

        :deep(.el-rate) {
          .el-rate__icon {
            font-size: 14px;
          }

          .el-rate__text {
            font-size: 12px;
            margin-left: 4px;
            color: #4a5568;
          }
        }
      }
    }

    .path-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px 20px;
      border-top: 1px solid #e2e8f0;

      .price-section {
        display: flex;
        align-items: center;
        gap: 8px;

        .original-price {
          font-size: 12px;
          color: #a0aec0;
          text-decoration: line-through;
        }

        .current-price {
          font-size: 18px;
          font-weight: 700;
          color: #667eea;
        }
      }

      .action-section {
        .start-btn {
          background: linear-gradient(135deg, #667eea, #764ba2);
          border: none;
          border-radius: 20px;
          padding: 8px 20px;
          font-size: 13px;
          font-weight: 500;
        }
      }
    }

    // 悬停覆盖层
    .hover-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(102, 126, 234, 0.95);
      color: white;
      padding: 20px;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      justify-content: center;
      backdrop-filter: blur(10px);

      .overlay-content {
        text-align: center;

        h4 {
          font-size: 16px;
          margin: 0 0 16px 0;
          font-weight: 600;
        }

        .course-preview {
          margin-bottom: 20px;

          .course-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);

            .course-title {
              font-size: 13px;
              flex: 1;
              text-align: left;
            }

            .course-duration {
              font-size: 12px;
              opacity: 0.8;
            }
          }

          .more-courses {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 8px;
          }
        }

        .preview-btn {
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: white;
          border-radius: 16px;
        }
      }
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 80px 20px;

  .empty-illustration {
    margin-bottom: 24px;

    .empty-icon {
      font-size: 64px;
      opacity: 0.6;
    }
  }

  .empty-title {
    font-size: 20px;
    color: #1a202c;
    margin: 0 0 12px 0;
    font-weight: 600;
  }

  .empty-description {
    font-size: 14px;
    color: #718096;
    margin: 0 0 24px 0;
    line-height: 1.5;
  }
}

// 动画效果
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .paths-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-section {
    .header-content {
      flex-direction: column;
      gap: 20px;

      .quick-filters {
        width: 100%;
        flex-direction: column;
        gap: 16px;

        .filter-group {
          .filter-chips {
            gap: 6px;

            .filter-chip {
              font-size: 12px;
              padding: 4px 8px;
            }
          }
        }

        .search-group {
          min-width: auto;
          width: 100%;
        }
      }
    }
  }

  .paths-section {
    .results-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;

      .results-info {
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
      }
    }
  }

  .paths-grid {
    grid-template-columns: 1fr;
    gap: 16px;

    .path-card {
      min-height: 280px;

      .path-stats {
        .stat-row {
          .stat-item {
            .stat-value {
              font-size: 12px;
            }

            .stat-label {
              font-size: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
