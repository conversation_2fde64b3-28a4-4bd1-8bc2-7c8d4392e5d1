// ===========================================
// 工具类系统 - Utility Classes System
// ===========================================

// 文本颜色工具类 - Text Color Utilities
.text-primary {
  color: var(--text-primary) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.text-tertiary {
  color: var(--text-tertiary) !important;
}

.text-disabled {
  color: var(--text-disabled) !important;
}

.text-inverse {
  color: var(--text-inverse) !important;
}

.text-brand {
  color: var(--brand-primary) !important;
}

.text-brand-secondary {
  color: var(--brand-secondary) !important;
}

.text-success {
  color: var(--color-success) !important;
}

.text-warning {
  color: var(--color-warning) !important;
}

.text-error {
  color: var(--color-error) !important;
}

.text-info {
  color: var(--color-info) !important;
}

// 背景颜色工具类 - Background Color Utilities
.bg-primary {
  background-color: var(--bg-primary) !important;
}

.bg-secondary {
  background-color: var(--bg-secondary) !important;
}

.bg-tertiary {
  background-color: var(--bg-tertiary) !important;
}

.bg-brand {
  background-color: var(--brand-primary) !important;
}

.bg-brand-secondary {
  background-color: var(--brand-secondary) !important;
}

.bg-success {
  background-color: var(--color-success) !important;
}

.bg-warning {
  background-color: var(--color-warning) !important;
}

.bg-error {
  background-color: var(--color-error) !important;
}

.bg-info {
  background-color: var(--color-info) !important;
}

// 边框颜色工具类 - Border Color Utilities
.border-primary {
  border-color: var(--border-primary) !important;
}

.border-secondary {
  border-color: var(--border-secondary) !important;
}

.border-focus {
  border-color: var(--border-focus) !important;
}

.border-error {
  border-color: var(--border-error) !important;
}

.border-brand {
  border-color: var(--brand-primary) !important;
}

// 字体大小工具类 - Font Size Utilities
.text-xs {
  font-size: var(--text-xs) !important;
}

.text-sm {
  font-size: var(--text-sm) !important;
}

.text-base {
  font-size: var(--text-base) !important;
}

.text-lg {
  font-size: var(--text-lg) !important;
}

.text-xl {
  font-size: var(--text-xl) !important;
}

.text-2xl {
  font-size: var(--text-2xl) !important;
}

.text-3xl {
  font-size: var(--text-3xl) !important;
}

.text-4xl {
  font-size: var(--text-4xl) !important;
}

.text-5xl {
  font-size: var(--text-5xl) !important;
}

// 字重工具类 - Font Weight Utilities
.font-thin {
  font-weight: var(--font-thin) !important;
}

.font-light {
  font-weight: var(--font-light) !important;
}

.font-normal {
  font-weight: var(--font-normal) !important;
}

.font-medium {
  font-weight: var(--font-medium) !important;
}

.font-semibold {
  font-weight: var(--font-semibold) !important;
}

.font-bold {
  font-weight: var(--font-bold) !important;
}

.font-extrabold {
  font-weight: var(--font-extrabold) !important;
}

.font-black {
  font-weight: var(--font-black) !important;
}

// 行高工具类 - Line Height Utilities
.leading-none {
  line-height: var(--leading-none) !important;
}

.leading-tight {
  line-height: var(--leading-tight) !important;
}

.leading-snug {
  line-height: var(--leading-snug) !important;
}

.leading-normal {
  line-height: var(--leading-normal) !important;
}

.leading-relaxed {
  line-height: var(--leading-relaxed) !important;
}

.leading-loose {
  line-height: var(--leading-loose) !important;
}

// 圆角工具类 - Border Radius Utilities
.rounded-none {
  border-radius: var(--radius-none) !important;
}

.rounded-sm {
  border-radius: var(--radius-sm) !important;
}

.rounded {
  border-radius: var(--radius-base) !important;
}

.rounded-md {
  border-radius: var(--radius-md) !important;
}

.rounded-lg {
  border-radius: var(--radius-lg) !important;
}

.rounded-xl {
  border-radius: var(--radius-xl) !important;
}

.rounded-2xl {
  border-radius: var(--radius-2xl) !important;
}

.rounded-full {
  border-radius: var(--radius-full) !important;
}

// 阴影工具类 - Shadow Utilities
.shadow-xs {
  box-shadow: var(--shadow-xs) !important;
}

.shadow-sm {
  box-shadow: var(--shadow-sm) !important;
}

.shadow-md {
  box-shadow: var(--shadow-md) !important;
}

.shadow-lg {
  box-shadow: var(--shadow-lg) !important;
}

.shadow-xl {
  box-shadow: var(--shadow-xl) !important;
}

.shadow-2xl {
  box-shadow: var(--shadow-2xl) !important;
}

.shadow-none {
  box-shadow: none !important;
}

// 过渡动画工具类 - Transition Utilities
.transition-fast {
  transition: all var(--transition-fast) !important;
}

.transition-base {
  transition: all var(--transition-base) !important;
}

.transition-slow {
  transition: all var(--transition-slow) !important;
}

.transition-none {
  transition: none !important;
}
