// 导入设计系统
@import './design-tokens.scss';
@import './typography.scss';
@import './layout.scss';
@import './utilities.scss';

// 兼容性变量 - 保持现有代码工作
:root {
  // 映射到新的设计token
  --primary-color: var(--brand-primary);
  --secondary-color: var(--brand-primary-light);
  --accent-color: var(--brand-accent);
  --text-primary: var(--text-primary);
  --text-secondary: var(--text-secondary);
  --text-light: var(--text-tertiary);
  --background-color: var(--bg-primary);
  --background-light: var(--bg-secondary);
  --border-color: var(--border-primary);
  --border-light: var(--border-secondary);
  --shadow-color: var(--shadow-md);
  --success-color: var(--color-success);
  --warning-color: var(--color-warning);
  --error-color: var(--color-error);

  // 新增紫色主题变量用于特定页面
  --purple-primary: var(--brand-secondary);
  --purple-secondary: var(--brand-secondary-light);
  --purple-light: var(--neutral-50);
  --purple-border: rgba(102, 126, 234, 0.2);
}

// 基础样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background-color);
}

// 容器样式 - 使用设计系统的容器宽度
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.section {
  padding: 60px 0;
}

// 卡片样式
.card {
  background: var(--background-color);
  border-radius: 12px;
  box-shadow: 0 4px 20px var(--shadow-color);
  padding: 24px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }
}

// 按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;

  &-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    }
  }

  &-secondary {
    background: var(--background-light);
    color: var(--text-primary);
    border: 1px solid var(--border-color);

    &:hover {
      background: var(--background-color);
      border-color: var(--primary-color);
    }
  }
}

// 标题样式
.title {
  font-weight: 600;
  margin-bottom: 16px;

  &-large {
    font-size: 36px;
    line-height: 1.2;
  }

  &-medium {
    font-size: 24px;
    line-height: 1.3;
  }

  &-small {
    font-size: 18px;
    line-height: 1.4;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .section {
    padding: 40px 0;
  }

  .title {
    &-large {
      font-size: 28px;
    }

    &-medium {
      font-size: 20px;
    }
  }
}

// 全局颜色统一覆盖 - Global Color Overrides
// 统一常见的硬编码颜色
[style*='color: #262626'],
[style*='color:#262626'] {
  color: var(--text-primary) !important;
}

[style*='color: #595959'],
[style*='color:#595959'] {
  color: var(--text-secondary) !important;
}

[style*='color: #8c8c8c'],
[style*='color:#8c8c8c'] {
  color: var(--text-tertiary) !important;
}

[style*='color: #1890ff'],
[style*='color:#1890ff'] {
  color: var(--brand-secondary) !important;
}

[style*='color: #ff4d4f'],
[style*='color:#ff4d4f'] {
  color: var(--color-error) !important;
}

[style*='color: #52c41a'],
[style*='color:#52c41a'] {
  color: var(--color-success) !important;
}

[style*='background: #f0f0f0'],
[style*='background:#f0f0f0'],
[style*='background-color: #f0f0f0'],
[style*='background-color:#f0f0f0'] {
  background-color: var(--bg-tertiary) !important;
}

// 全局样式统一 - Global Style Unification
// 统一页面标题样式
.page-title {
  font-size: var(--text-4xl) !important;
  font-weight: var(--font-bold) !important;
  color: var(--text-primary) !important;
  line-height: var(--leading-tight) !important;
  margin-bottom: var(--space-4) !important;
}

.page-subtitle {
  font-size: var(--text-lg) !important;
  color: var(--text-secondary) !important;
  line-height: var(--leading-normal) !important;
  margin-bottom: var(--space-6) !important;
}

// 统一课程卡片标题
.course-title {
  font-size: var(--text-lg) !important;
  font-weight: var(--font-semibold) !important;
  color: var(--text-primary) !important;
  line-height: var(--leading-snug) !important;
}

// 统一讲师姓名
.instructor-name {
  font-size: var(--text-base) !important;
  color: var(--text-secondary) !important;
  font-weight: var(--font-medium) !important;
}

// 统一描述文字
.course-description,
.instructor-description {
  font-size: var(--text-sm) !important;
  color: var(--text-secondary) !important;
  line-height: var(--leading-relaxed) !important;
}

// 统一元信息
.course-meta,
.instructor-meta {
  font-size: var(--text-xs) !important;
  color: var(--text-tertiary) !important;
}

// 统一价格显示
.course-price .current-price {
  font-size: var(--text-lg) !important;
  font-weight: var(--font-semibold) !important;
  color: var(--color-error) !important;
}

.course-price .original-price {
  font-size: var(--text-sm) !important;
  color: var(--text-tertiary) !important;
}

// 工具类
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-light {
  color: var(--text-light);
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mb-32 {
  margin-bottom: 32px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

.mt-32 {
  margin-top: 32px;
}
